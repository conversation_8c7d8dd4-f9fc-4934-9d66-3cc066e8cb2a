#!/bin/bash

# AgentChain Project Setup Script
echo "Setting up AgentChain project structure..."

# Create necessary directories
mkdir -p cmd/agentchaind
mkdir -p app
mkdir -p x/agent/keeper
mkdir -p x/agent/types
mkdir -p x/agent/client/cli
mkdir -p testutil/keeper
mkdir -p proto/agent

# Initialize go modules if not exists
if [ ! -f go.mod ]; then
    go mod init github.com/seniortechdev/agentchain
fi

echo "Project structure created successfully!"
echo "Next steps:"
echo "1. Run: chmod +x setup_project.sh && ./setup_project.sh"
echo "2. Run: go mod tidy"
echo "3. Follow the testing guide below"
