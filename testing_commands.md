# AgentChain Testing Commands Guide

## 🔧 Build and Setup Commands

### 1. Initialize Go Modules
```bash
go mod init github.com/seniortechdev/agentchain
go mod tidy
```

### 2. Build the Chain Binary
```bash
# Using Makefile
make build

# Or directly with go
go build -o build/agentchaind ./cmd/agentchaind
```

### 3. Initialize Chain Configuration
```bash
# Initialize chain
./build/agentchaind init mynode --chain-id agentchain

# Add a test key
./build/agentchaind keys add alice
./build/agentchaind keys add bob

# Add genesis account
./build/agentchaind add-genesis-account alice 100000000stake
./build/agentchaind add-genesis-account bob 100000000stake

# Create genesis transaction
./build/agentchaind gentx alice 1000000stake --chain-id agentchain

# Collect genesis transactions
./build/agentchaind collect-gentxs
```

### 4. Start the Chain
```bash
# Start the blockchain
./build/agentchaind start
```

## 🧪 Testing Workflow Commands

### 1. Create Test Wallets
```bash
# Create test accounts
./build/agentchaind keys add testuser1
./build/agentchaind keys add testuser2

# Get addresses
TESTUSER1=$(./build/agentchaind keys show testuser1 -a)
TESTUSER2=$(./build/agentchaind keys show testuser2 -a)

echo "Test User 1: $TESTUSER1"
echo "Test User 2: $TESTUSER2"
```

### 2. Create Agents
```bash
# Create first agent
./build/agentchaind tx agent create-agent "My First Agent" "This is my first test agent" \
  --from testuser1 \
  --chain-id agentchain \
  --gas auto \
  --gas-adjustment 1.5 \
  --fees 1000stake \
  -y

# Create second agent
./build/agentchaind tx agent create-agent "AI Assistant" "An intelligent AI assistant agent" \
  --from testuser2 \
  --chain-id agentchain \
  --gas auto \
  --gas-adjustment 1.5 \
  --fees 1000stake \
  -y

# Create third agent (same user)
./build/agentchaind tx agent create-agent "Data Processor" "Agent for processing data" \
  --from testuser1 \
  --chain-id agentchain \
  --gas auto \
  --gas-adjustment 1.5 \
  --fees 1000stake \
  -y
```

### 3. Query Agents
```bash
# Query agent by ID
./build/agentchaind query agent agent 1
./build/agentchaind query agent agent 2
./build/agentchaind query agent agent 3

# Query non-existent agent (should return error)
./build/agentchaind query agent agent 999
```

### 4. Update Agents
```bash
# Update agent description (by owner)
./build/agentchaind tx agent update-agent 1 "Updated description for my first agent" \
  --from testuser1 \
  --chain-id agentchain \
  --gas auto \
  --gas-adjustment 1.5 \
  --fees 1000stake \
  -y

# Try to update agent by non-owner (should fail)
./build/agentchaind tx agent update-agent 1 "Unauthorized update attempt" \
  --from testuser2 \
  --chain-id agentchain \
  --gas auto \
  --gas-adjustment 1.5 \
  --fees 1000stake \
  -y

# Update another agent
./build/agentchaind tx agent update-agent 2 "Enhanced AI assistant with new capabilities" \
  --from testuser2 \
  --chain-id agentchain \
  --gas auto \
  --gas-adjustment 1.5 \
  --fees 1000stake \
  -y
```

### 5. Verify Events
```bash
# Query transaction events
./build/agentchaind query tx [TRANSACTION_HASH]

# Example: Look for create_agent and update_agent events
# Events should contain:
# - agent_id
# - owner
# - event type (create_agent or update_agent)
```

## 🔍 Validation Checks

### 1. Agent ID Auto-increment Validation
```bash
# Create multiple agents and verify IDs increment
for i in {1..5}; do
  ./build/agentchaind tx agent create-agent "Agent $i" "Description for agent $i" \
    --from testuser1 \
    --chain-id agentchain \
    --gas auto \
    --gas-adjustment 1.5 \
    --fees 1000stake \
    -y
  sleep 2
done

# Query all created agents
for i in {1..8}; do
  echo "=== Agent $i ==="
  ./build/agentchaind query agent agent $i
  echo ""
done
```

### 2. Data Storage Validation
```bash
# Verify agent data persistence after chain restart
./build/agentchaind query agent agent 1
# Stop chain (Ctrl+C)
# Restart chain
./build/agentchaind start
# Query again to verify persistence
./build/agentchaind query agent agent 1
```

### 3. Event Emission Validation
```bash
# Create agent and capture transaction hash
TX_HASH=$(./build/agentchaind tx agent create-agent "Event Test Agent" "Testing event emission" \
  --from testuser1 \
  --chain-id agentchain \
  --gas auto \
  --gas-adjustment 1.5 \
  --fees 1000stake \
  -y \
  --output json | jq -r '.txhash')

echo "Transaction Hash: $TX_HASH"

# Wait for transaction to be included in block
sleep 5

# Query transaction details to see events
./build/agentchaind query tx $TX_HASH
```

## 🐛 Debugging Commands

### 1. Check Chain Status
```bash
# Check if chain is running
./build/agentchaind status

# Check latest block
./build/agentchaind query block

# Check account balances
./build/agentchaind query bank balances $TESTUSER1
```

### 2. Validate Transactions
```bash
# Check transaction pool
./build/agentchaind query txs --events 'message.module=agent'

# Check specific transaction
./build/agentchaind query tx [TX_HASH] --output json | jq
```

### 3. Module-specific Debugging
```bash
# Check module parameters
./build/agentchaind query agent params

# Check module state
./build/agentchaind export | jq '.app_state.agent'
```

## 📊 Expected Outputs

### Successful Agent Creation
```json
{
  "height": "123",
  "txhash": "ABC123...",
  "code": 0,
  "events": [
    {
      "type": "create_agent",
      "attributes": [
        {"key": "agent_id", "value": "1"},
        {"key": "owner", "value": "cosmos1..."}
      ]
    }
  ]
}
```

### Successful Agent Query
```json
{
  "agent": {
    "id": "1",
    "owner": "cosmos1abc123...",
    "name": "My First Agent",
    "description": "This is my first test agent",
    "created_at": "**********"
  }
}
```

### Failed Update (Non-owner)
```json
{
  "code": 4,
  "raw_log": "only the owner can update the agent"
}
```
