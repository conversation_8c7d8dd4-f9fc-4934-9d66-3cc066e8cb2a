# 🐛 Agent<PERSON>hain Debugging Guide

## Common Issues and Solutions

### 1. Build Issues

#### Problem: "cannot find package" errors
```bash
Error: cannot find package "github.com/cosmos/cosmos-sdk/types"
```

**Solution:**
```bash
# Ensure go.mod is properly configured
go mod init github.com/seniortechdev/agentchain
go mod tidy

# If using specific Cosmos SDK version
go get github.com/cosmos/cosmos-sdk@v0.50.1
```

#### Problem: "undefined: msgServer" errors
**Solution:** Ensure your keeper has the proper msgServer struct:
```go
type msgServer struct {
    Keeper
}

func NewMsgServerImpl(keeper Keeper) types.MsgServer {
    return &msgServer{Keeper: keeper}
}
```

### 2. Runtime Issues

#### Problem: Chain fails to start
```bash
Error: failed to initialize database
```

**Solutions:**
```bash
# Clean chain data
rm -rf ~/.agentchain

# Reinitialize
./agentchaind init mynode --chain-id agentchain

# Check permissions
chmod -R 755 ~/.agentchain
```

#### Problem: Transaction fails with "insufficient gas"
**Solution:**
```bash
# Use auto gas estimation
--gas auto --gas-adjustment 1.5

# Or specify higher gas limit
--gas 200000
```

#### Problem: "account sequence mismatch"
**Solution:**
```bash
# Query account to check sequence
./agentchaind query auth account [ADDRESS]

# Wait for previous transaction to be included
sleep 5

# Or use --sequence flag with correct value
--sequence [CORRECT_SEQUENCE]
```

### 3. Agent Module Specific Issues

#### Problem: "agent not found" when it should exist
**Debugging steps:**
```bash
# Check if transaction was successful
./agentchaind query tx [TX_HASH]

# Verify transaction was included in block
./agentchaind query block [BLOCK_HEIGHT]

# Check module state
./agentchaind export | jq '.app_state.agent'

# Verify agent ID counter
./agentchaind query agent params
```

#### Problem: Events not being emitted
**Debugging:**
```bash
# Check transaction details
./agentchaind query tx [TX_HASH] --output json | jq '.events'

# Look for specific event types
./agentchaind query txs --events 'message.module=agent'

# Check if events are in logs
./agentchaind query tx [TX_HASH] --output json | jq '.logs'
```

#### Problem: Update fails with permission error
**Verification:**
```bash
# Check agent owner
./agentchaind query agent agent [ID] | jq '.agent.owner'

# Check transaction sender
./agentchaind keys show [KEY_NAME] -a

# Ensure addresses match exactly
```

### 4. Data Validation Issues

#### Problem: Agent IDs not auto-incrementing
**Debug steps:**
```bash
# Check NextAgentID in state
./agentchaind export | jq '.app_state.agent.next_agent_id'

# Verify SetNextAgentID is being called
# Add logging to your keeper methods

# Check if multiple agents have same ID
./agentchaind export | jq '.app_state.agent.agents'
```

#### Problem: Data not persisting after restart
**Solutions:**
```bash
# Ensure proper KVStore usage in keeper
# Check that data directory has write permissions
ls -la ~/.agentchain/data

# Verify no database corruption
./agentchaind export > state_backup.json
```

## 🔧 Debugging Tools and Commands

### 1. State Inspection
```bash
# Export entire chain state
./agentchaind export > chain_state.json

# Check specific module state
./agentchaind export | jq '.app_state.agent'

# Query specific agent
./agentchaind query agent agent [ID] --output json
```

### 2. Transaction Analysis
```bash
# Get transaction details
./agentchaind query tx [TX_HASH] --output json

# Search transactions by events
./agentchaind query txs --events 'message.module=agent'
./agentchaind query txs --events 'create_agent.agent_id=1'

# Check transaction in mempool
./agentchaind query txs --events 'tx.hash=[TX_HASH]'
```

### 3. Account and Balance Checks
```bash
# Check account details
./agentchaind query auth account [ADDRESS]

# Check balances
./agentchaind query bank balances [ADDRESS]

# List all keys
./agentchaind keys list
```

### 4. Block and Consensus Info
```bash
# Get latest block
./agentchaind query block

# Get specific block
./agentchaind query block [HEIGHT]

# Check validator set
./agentchaind query tendermint-validator-set

# Check chain status
./agentchaind status
```

## 🧪 Testing Strategies

### 1. Unit Testing
Create unit tests for your keeper methods:
```go
func TestCreateAgent(t *testing.T) {
    // Setup test environment
    // Call CreateAgent
    // Assert expected behavior
}
```

### 2. Integration Testing
Use the provided integration_test.sh script:
```bash
chmod +x integration_test.sh
./integration_test.sh
```

### 3. Load Testing
Test with multiple concurrent transactions:
```bash
# Create multiple agents simultaneously
for i in {1..10}; do
  ./agentchaind tx agent create-agent "Agent $i" "Description $i" \
    --from alice --chain-id agentchain -y &
done
wait
```

### 4. Edge Case Testing
```bash
# Test with empty strings
./agentchaind tx agent create-agent "" "" --from alice

# Test with very long strings
./agentchaind tx agent create-agent "$(head -c 1000 /dev/zero | tr '\0' 'a')" "test"

# Test with special characters
./agentchaind tx agent create-agent "Agent with 特殊字符" "Description with émojis 🚀"

# Test with non-existent agent ID
./agentchaind tx agent update-agent 99999 "test" --from alice
```

## 📊 Performance Monitoring

### 1. Transaction Throughput
```bash
# Monitor transaction processing
watch './agentchaind query block | jq ".block.header.height"'

# Check mempool size
./agentchaind query mempool
```

### 2. Resource Usage
```bash
# Monitor process resources
top -p $(pgrep agentchaind)

# Check disk usage
du -sh ~/.agentchain

# Monitor network connections
netstat -tulpn | grep agentchaind
```

### 3. Log Analysis
```bash
# View chain logs
tail -f ~/.agentchain/logs/agentchaind.log

# Filter for agent module logs
grep "agent" ~/.agentchain/logs/agentchaind.log

# Check for errors
grep -i error ~/.agentchain/logs/agentchaind.log
```

## 🚨 Emergency Procedures

### 1. Chain Recovery
```bash
# If chain becomes unresponsive
pkill agentchaind

# Reset to last known good state
./agentchaind unsafe-reset-all

# Restore from backup
cp chain_state_backup.json ~/.agentchain/config/genesis.json
```

### 2. Data Corruption
```bash
# Verify chain integrity
./agentchaind validate-genesis

# Export and re-import state
./agentchaind export > backup.json
./agentchaind unsafe-reset-all
./agentchaind import backup.json
```

### 3. Performance Issues
```bash
# Increase cache size
./agentchaind start --db_backend=goleveldb --cache_size=10000

# Reduce logging
./agentchaind start --log_level=error

# Optimize pruning
./agentchaind start --pruning=everything
```
