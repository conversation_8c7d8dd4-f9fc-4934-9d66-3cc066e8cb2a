package types

import "fmt"

// DefaultIndex is the default global index
const DefaultIndex uint64 = 1

// DefaultGenesis returns the default genesis state
func DefaultGenesis() *GenesisState {
	return &GenesisState{
		AgentList:   []Agent{},
		NextAgentId: DefaultIndex,
	}
}

// Validate performs basic genesis state validation returning an error upon any
// failure.
func (gs GenesisState) Validate() error {
	// Check for duplicated index in agent
	agentIndexMap := make(map[uint64]struct{})

	for _, elem := range gs.AgentList {
		index := elem.Id
		if _, ok := agentIndexMap[index]; ok {
			return fmt.<PERSON><PERSON>rf("duplicated index for agent")
		}
		agentIndexMap[index] = struct{}{}
	}

	return nil
}

// GenesisState defines the agent module's genesis state.
type GenesisState struct {
	AgentList   []Agent `json:"agent_list"`
	NextAgentId uint64  `json:"next_agent_id"`
}
