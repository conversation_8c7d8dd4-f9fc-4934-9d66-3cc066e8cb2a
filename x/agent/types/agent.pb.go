type Agent struct {
    Id          uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
    Owner       string `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
    Name        string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
    Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
    CreatedAt   int64  `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}