func (k Keeper) SetAgent(ctx sdk.Context, agent types.Agent) {
    store := prefix.NewStore(ctx.KVStore(k.storeKey), types.KeyPrefix(types.AgentKey))
    b := k.cdc.MustMarshal(&agent)
    store.Set(types.<PERSON><PERSON><PERSON>(agent.Id), b)
}

func (k Keeper) GetAgent(ctx sdk.Context, id uint64) (val types.Agent, found bool) {
    store := prefix.NewStore(ctx.KVStore(k.storeKey), types.KeyPrefix(types.AgentKey))
    b := store.Get(types.AgentKey(id))
    if b == nil {
        return val, false
    }
    k.cdc.MustUnmarshal(b, &val)
    return val, true
}

func (k Keeper) GetNextAgentID(ctx sdk.Context) uint64 {
    store := ctx.KVStore(k.storeKey)
    bz := store.Get(types.NextAgentIDKey)
    if bz == nil {
        return 1
    }
    return binary.BigEndian.Uint64(bz)
}

func (k Keeper) SetNextAgentID(ctx sdk.Context, id uint64) {
    store := ctx.KVStore(k.storeKey)
    bz := make([]byte, 8)
    binary.BigEndian.PutUint64(bz, id)
    store.Set(types.NextAgentIDKey, bz)
}