func (k Keeper) Agent(goCtx context.Context, req *types.QueryAgentRequest) (*types.QueryAgentResponse, error) {
    if req == nil {
        return nil, status.Error(codes.InvalidArgument, "invalid request")
    }

    ctx := sdk.UnwrapSDKContext(goCtx)
    agent, found := k.<PERSON>(ctx, req.Id)
    if !found {
        return nil, status.Error(codes.NotFound, "agent not found")
    }

    return &types.QueryAgentResponse{Agent: agent}, nil
}