func (k msgServer) CreateAgent(goCtx context.Context, msg *types.MsgCreateAgent) (*types.MsgCreateAgentResponse, error) {
    ctx := sdk.UnwrapSDKContext(goCtx)

    // Auto-increment agent ID
    nextID := k.GetNextAgentID(ctx)
    
    // Create agent object
    agent := types.Agent{
        Id:          nextID,
        Owner:       msg.Creator,
        Name:        msg.Name,
        Description: msg.Description,
        CreatedAt:   ctx.BlockTime().Unix(),
    }

    // Store agent in KVStore
    k.SetAgent(ctx, agent)
    k.SetNextAgentID(ctx, nextID+1)

    // Emit event
    ctx.EventManager().EmitEvent(
        sdk.NewEvent(
            types.EventTypeCreateAgent,
            sdk.NewAttribute(types.AttributeKeyAgentID, fmt.Sprintf("%d", nextID)),
            sdk.NewAttribute(types.AttributeKeyOwner, msg.Creator),
        ),
    )

    return &types.MsgCreateAgentResponse{Id: nextID}, nil
}