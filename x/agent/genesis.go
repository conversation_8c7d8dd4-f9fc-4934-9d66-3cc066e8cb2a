package agent

import (
	sdk "github.com/cosmos/cosmos-sdk/types"
	"github.com/seniortechdev/agentchain/x/agent/keeper"
	"github.com/seniortechdev/agentchain/x/agent/types"
)

// InitGenesis initializes the module's state from a provided genesis state.
func InitGenesis(ctx sdk.Context, k keeper.Keeper, genState types.GenesisState) {
	// Set all the agents
	for _, elem := range genState.AgentList {
		k.SetAgent(ctx, elem)
	}

	// Set agent count
	k.SetNextAgentID(ctx, genState.NextAgentId)
}

// ExportGenesis returns the module's exported genesis
func ExportGenesis(ctx sdk.Context, k keeper.Keeper) types.GenesisState {
	genesis := types.DefaultGenesis()

	// Get all agents
	// Note: You would need to implement GetAllAgents in keeper
	// For now, we'll use an empty list
	genesis.AgentList = []types.Agent{}

	// Get next agent ID
	genesis.NextAgentId = k.GetNextAgentID(ctx)

	return *genesis
}
