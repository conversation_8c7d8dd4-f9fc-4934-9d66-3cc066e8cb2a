package main

import (
	"fmt"
	"time"
)

// Mock structures for testing (simplified versions of your actual types)
type Agent struct {
	Id          uint64 `json:"id"`
	Owner       string `json:"owner"`
	Name        string `json:"name"`
	Description string `json:"description"`
	CreatedAt   int64  `json:"created_at"`
}

type MsgCreateAgent struct {
	Creator     string `json:"creator"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

type MsgUpdateAgent struct {
	Creator     string `json:"creator"`
	Id          uint64 `json:"id"`
	Description string `json:"description"`
}

// Mock storage for testing
var agentStore = make(map[uint64]Agent)
var nextAgentID uint64 = 1

// Mock functions to simulate your keeper logic
func CreateAgent(msg MsgCreateAgent) (uint64, error) {
	// Validate input
	if msg.Name == "" {
		return 0, fmt.Errorf("name cannot be empty")
	}
	if msg.Description == "" {
		return 0, fmt.<PERSON><PERSON><PERSON>("description cannot be empty")
	}
	if msg.Creator == "" {
		return 0, fmt.Errorf("creator cannot be empty")
	}

	// Create agent
	agent := Agent{
		Id:          nextAgentID,
		Owner:       msg.Creator,
		Name:        msg.Name,
		Description: msg.Description,
		CreatedAt:   time.Now().Unix(),
	}

	// Store agent
	agentStore[nextAgentID] = agent
	currentID := nextAgentID
	nextAgentID++

	fmt.Printf("✅ Agent created successfully: ID=%d, Owner=%s, Name=%s\n",
		currentID, agent.Owner, agent.Name)

	return currentID, nil
}

func UpdateAgent(msg MsgUpdateAgent) error {
	// Get existing agent
	agent, exists := agentStore[msg.Id]
	if !exists {
		return fmt.Errorf("agent with ID %d not found", msg.Id)
	}

	// Check ownership
	if agent.Owner != msg.Creator {
		return fmt.Errorf("only the owner can update the agent")
	}

	// Validate input
	if msg.Description == "" {
		return fmt.Errorf("description cannot be empty")
	}

	// Update agent
	agent.Description = msg.Description
	agentStore[msg.Id] = agent

	fmt.Printf("✅ Agent updated successfully: ID=%d, New Description=%s\n",
		msg.Id, agent.Description)

	return nil
}

func GetAgent(id uint64) (Agent, error) {
	agent, exists := agentStore[id]
	if !exists {
		return Agent{}, fmt.Errorf("agent with ID %d not found", id)
	}
	return agent, nil
}

// Test functions
func TestCreateAgent() {
	fmt.Println("\n🧪 Testing CreateAgent functionality...")

	// Test 1: Valid agent creation
	msg1 := MsgCreateAgent{
		Creator:     "cosmos1abc123def456ghi789jkl012mno345pqr678stu",
		Name:        "Test Agent 1",
		Description: "This is a test agent for validation",
	}

	id1, err := CreateAgent(msg1)
	if err != nil {
		fmt.Printf("❌ Test 1 failed: %v\n", err)
	} else {
		fmt.Printf("✅ Test 1 passed: Agent created with ID %d\n", id1)
	}

	// Test 2: Another valid agent creation (test auto-increment)
	msg2 := MsgCreateAgent{
		Creator:     "cosmos1xyz789abc123def456ghi789jkl012mno345pqr",
		Name:        "Test Agent 2",
		Description: "Second test agent",
	}

	id2, err := CreateAgent(msg2)
	if err != nil {
		fmt.Printf("❌ Test 2 failed: %v\n", err)
	} else {
		fmt.Printf("✅ Test 2 passed: Agent created with ID %d (auto-increment working)\n", id2)
	}

	// Test 3: Invalid agent creation (empty name)
	msg3 := MsgCreateAgent{
		Creator:     "cosmos1test123",
		Name:        "",
		Description: "Test description",
	}

	_, err = CreateAgent(msg3)
	if err != nil {
		fmt.Printf("✅ Test 3 passed: Correctly rejected empty name: %v\n", err)
	} else {
		fmt.Printf("❌ Test 3 failed: Should have rejected empty name\n")
	}
}

func TestUpdateAgent() {
	fmt.Println("\n🧪 Testing UpdateAgent functionality...")

	// Test 1: Valid update by owner
	msg1 := MsgUpdateAgent{
		Creator:     "cosmos1abc123def456ghi789jkl012mno345pqr678stu",
		Id:          1,
		Description: "Updated description for agent 1",
	}

	err := UpdateAgent(msg1)
	if err != nil {
		fmt.Printf("❌ Test 1 failed: %v\n", err)
	} else {
		fmt.Printf("✅ Test 1 passed: Agent updated successfully\n")
	}

	// Test 2: Invalid update by non-owner
	msg2 := MsgUpdateAgent{
		Creator:     "cosmos1different123",
		Id:          1,
		Description: "Unauthorized update attempt",
	}

	err = UpdateAgent(msg2)
	if err != nil {
		fmt.Printf("✅ Test 2 passed: Correctly rejected unauthorized update: %v\n", err)
	} else {
		fmt.Printf("❌ Test 2 failed: Should have rejected unauthorized update\n")
	}

	// Test 3: Update non-existent agent
	msg3 := MsgUpdateAgent{
		Creator:     "cosmos1abc123def456ghi789jkl012mno345pqr678stu",
		Id:          999,
		Description: "Update non-existent agent",
	}

	err = UpdateAgent(msg3)
	if err != nil {
		fmt.Printf("✅ Test 3 passed: Correctly rejected non-existent agent: %v\n", err)
	} else {
		fmt.Printf("❌ Test 3 failed: Should have rejected non-existent agent\n")
	}
}

func TestQueryAgent() {
	fmt.Println("\n🧪 Testing QueryAgent functionality...")

	// Test 1: Query existing agent
	agent, err := GetAgent(1)
	if err != nil {
		fmt.Printf("❌ Test 1 failed: %v\n", err)
	} else {
		fmt.Printf("✅ Test 1 passed: Retrieved agent: %+v\n", agent)
	}

	// Test 2: Query non-existent agent
	_, err = GetAgent(999)
	if err != nil {
		fmt.Printf("✅ Test 2 passed: Correctly handled non-existent agent: %v\n", err)
	} else {
		fmt.Printf("❌ Test 2 failed: Should have returned error for non-existent agent\n")
	}
}

func TestDataIntegrity() {
	fmt.Println("\n🧪 Testing Data Integrity...")

	// Verify agent IDs are auto-incrementing
	fmt.Printf("Next Agent ID: %d\n", nextAgentID)
	fmt.Printf("Total agents in store: %d\n", len(agentStore))

	// Display all agents
	fmt.Println("All agents in store:")
	for id, agent := range agentStore {
		fmt.Printf("  ID: %d, Owner: %s, Name: %s, Description: %s, CreatedAt: %d\n",
			id, agent.Owner, agent.Name, agent.Description, agent.CreatedAt)
	}
}

func main() {
	fmt.Println("🚀 AgentChain Module Testing Suite")
	fmt.Println("=====================================")

	TestCreateAgent()
	TestUpdateAgent()
	TestQueryAgent()
	TestDataIntegrity()

	fmt.Println("\n🎉 Testing completed!")
	fmt.Println("\nNext steps:")
	fmt.Println("1. Set up the complete Cosmos SDK project structure")
	fmt.Println("2. Run integration tests with actual blockchain")
	fmt.Println("3. Test with CLI commands")
}
