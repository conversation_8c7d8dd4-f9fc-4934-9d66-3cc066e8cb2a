#!/bin/bash

# Agent<PERSON>hain Integration Test Script
set -e

echo "🚀 Starting AgentChain Integration Tests"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test configuration
CHAIN_ID="agentchain-test"
BINARY="./build/agentchaind"
HOME_DIR="$HOME/.agentchain-test"

# Clean up function
cleanup() {
    echo -e "${YELLOW}Cleaning up test environment...${NC}"
    pkill -f agentchaind || true
    rm -rf "$HOME_DIR"
}

# Set up trap for cleanup
trap cleanup EXIT

# Function to wait for chain to be ready
wait_for_chain() {
    echo "Waiting for chain to be ready..."
    for i in {1..30}; do
        if $BINARY status --home "$HOME_DIR" &>/dev/null; then
            echo -e "${GREEN}Chain is ready!${NC}"
            return 0
        fi
        sleep 1
    done
    echo -e "${RED}Chain failed to start${NC}"
    exit 1
}

# Function to check command success
check_success() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1${NC}"
    else
        echo -e "${RED}❌ $1${NC}"
        exit 1
    fi
}

echo "📋 Step 1: Setting up test environment"
echo "--------------------------------------"

# Clean previous test data
rm -rf "$HOME_DIR"

# Initialize chain
$BINARY init testnode --chain-id $CHAIN_ID --home "$HOME_DIR"
check_success "Chain initialized"

# Create test keys
echo "test1234" | $BINARY keys add alice --keyring-backend test --home "$HOME_DIR"
echo "test1234" | $BINARY keys add bob --keyring-backend test --home "$HOME_DIR"
check_success "Test keys created"

# Get addresses
ALICE_ADDR=$($BINARY keys show alice -a --keyring-backend test --home "$HOME_DIR")
BOB_ADDR=$($BINARY keys show bob -a --keyring-backend test --home "$HOME_DIR")

echo "Alice address: $ALICE_ADDR"
echo "Bob address: $BOB_ADDR"

# Add genesis accounts
$BINARY add-genesis-account alice 100000000stake --keyring-backend test --home "$HOME_DIR"
$BINARY add-genesis-account bob 100000000stake --keyring-backend test --home "$HOME_DIR"
check_success "Genesis accounts added"

# Create genesis transaction
echo "test1234" | $BINARY gentx alice 1000000stake --chain-id $CHAIN_ID --keyring-backend test --home "$HOME_DIR"
check_success "Genesis transaction created"

# Collect genesis transactions
$BINARY collect-gentxs --home "$HOME_DIR"
check_success "Genesis transactions collected"

echo ""
echo "🚀 Step 2: Starting the chain"
echo "------------------------------"

# Start chain in background
$BINARY start --home "$HOME_DIR" &
CHAIN_PID=$!

# Wait for chain to be ready
wait_for_chain

echo ""
echo "🧪 Step 3: Testing Agent CRUD Operations"
echo "----------------------------------------"

# Test 1: Create first agent
echo "Test 1: Creating first agent..."
TX1=$(echo "test1234" | $BINARY tx agent create-agent "Test Agent 1" "First test agent" \
    --from alice \
    --chain-id $CHAIN_ID \
    --keyring-backend test \
    --home "$HOME_DIR" \
    --gas auto \
    --gas-adjustment 1.5 \
    --fees 1000stake \
    -y \
    --output json | jq -r '.txhash')

sleep 3
check_success "First agent creation transaction submitted"

# Test 2: Create second agent
echo "Test 2: Creating second agent..."
TX2=$(echo "test1234" | $BINARY tx agent create-agent "Test Agent 2" "Second test agent" \
    --from bob \
    --chain-id $CHAIN_ID \
    --keyring-backend test \
    --home "$HOME_DIR" \
    --gas auto \
    --gas-adjustment 1.5 \
    --fees 1000stake \
    -y \
    --output json | jq -r '.txhash')

sleep 3
check_success "Second agent creation transaction submitted"

# Test 3: Query agents
echo "Test 3: Querying agents..."
AGENT1=$($BINARY query agent agent 1 --home "$HOME_DIR" --output json)
AGENT2=$($BINARY query agent agent 2 --home "$HOME_DIR" --output json)

echo "Agent 1: $AGENT1"
echo "Agent 2: $AGENT2"
check_success "Agent queries successful"

# Test 4: Update agent by owner
echo "Test 4: Updating agent by owner..."
TX3=$(echo "test1234" | $BINARY tx agent update-agent 1 "Updated description for agent 1" \
    --from alice \
    --chain-id $CHAIN_ID \
    --keyring-backend test \
    --home "$HOME_DIR" \
    --gas auto \
    --gas-adjustment 1.5 \
    --fees 1000stake \
    -y \
    --output json | jq -r '.txhash')

sleep 3
check_success "Agent update by owner successful"

# Test 5: Try to update agent by non-owner (should fail)
echo "Test 5: Attempting unauthorized update..."
TX4=$(echo "test1234" | $BINARY tx agent update-agent 1 "Unauthorized update" \
    --from bob \
    --chain-id $CHAIN_ID \
    --keyring-backend test \
    --home "$HOME_DIR" \
    --gas auto \
    --gas-adjustment 1.5 \
    --fees 1000stake \
    -y \
    --output json | jq -r '.txhash' 2>/dev/null || echo "FAILED_AS_EXPECTED")

sleep 3
if [ "$TX4" = "FAILED_AS_EXPECTED" ]; then
    echo -e "${GREEN}✅ Unauthorized update correctly rejected${NC}"
else
    echo -e "${RED}❌ Unauthorized update should have failed${NC}"
fi

# Test 6: Query non-existent agent
echo "Test 6: Querying non-existent agent..."
RESULT=$($BINARY query agent agent 999 --home "$HOME_DIR" 2>&1 || echo "NOT_FOUND")
if [[ "$RESULT" == *"NOT_FOUND"* ]] || [[ "$RESULT" == *"not found"* ]]; then
    echo -e "${GREEN}✅ Non-existent agent query correctly handled${NC}"
else
    echo -e "${RED}❌ Non-existent agent query should have failed${NC}"
fi

echo ""
echo "🔍 Step 4: Validating Events and Data Integrity"
echo "-----------------------------------------------"

# Check transaction events
echo "Checking transaction events..."
if [ "$TX1" != "" ]; then
    TX1_DETAILS=$($BINARY query tx $TX1 --home "$HOME_DIR" --output json)
    echo "Transaction 1 events: $(echo $TX1_DETAILS | jq '.events')"
fi

if [ "$TX2" != "" ]; then
    TX2_DETAILS=$($BINARY query tx $TX2 --home "$HOME_DIR" --output json)
    echo "Transaction 2 events: $(echo $TX2_DETAILS | jq '.events')"
fi

# Verify agent data after update
echo "Verifying updated agent data..."
UPDATED_AGENT=$($BINARY query agent agent 1 --home "$HOME_DIR" --output json)
echo "Updated Agent 1: $UPDATED_AGENT"

# Check if description was updated
DESCRIPTION=$(echo $UPDATED_AGENT | jq -r '.agent.description')
if [[ "$DESCRIPTION" == *"Updated description"* ]]; then
    echo -e "${GREEN}✅ Agent description successfully updated${NC}"
else
    echo -e "${RED}❌ Agent description update failed${NC}"
fi

echo ""
echo "📊 Step 5: Final Validation Summary"
echo "-----------------------------------"

# Count total agents
echo "Checking total agents created..."
AGENT_COUNT=0
for i in {1..10}; do
    if $BINARY query agent agent $i --home "$HOME_DIR" &>/dev/null; then
        ((AGENT_COUNT++))
    else
        break
    fi
done

echo "Total agents created: $AGENT_COUNT"

if [ $AGENT_COUNT -eq 2 ]; then
    echo -e "${GREEN}✅ Correct number of agents created${NC}"
else
    echo -e "${RED}❌ Expected 2 agents, found $AGENT_COUNT${NC}"
fi

echo ""
echo "🎉 Integration Tests Completed!"
echo "==============================="
echo -e "${GREEN}All core functionality validated:${NC}"
echo "✅ Agent creation with auto-incrementing IDs"
echo "✅ Agent querying"
echo "✅ Agent updates by owner"
echo "✅ Authorization checks"
echo "✅ Event emission"
echo "✅ Data persistence"
echo ""
echo -e "${YELLOW}Chain is still running. Press Ctrl+C to stop.${NC}"

# Keep script running to maintain chain
wait $CHAIN_PID
